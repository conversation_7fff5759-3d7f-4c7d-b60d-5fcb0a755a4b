'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Card,
  Button,
  TextField,
  TextArea,
  Select,
  Badge,
  Checkbox,
  Text,
  Heading,
  Box,
  Flex,
  Separator,
} from '@radix-ui/themes';
import { Search, Plus, X, Eye, Lock, Loader2 } from 'lucide-react';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useRules } from '@/hooks/use-rule-queries';
import { Rule } from '@/lib/store';
import { CreateRulesetData, UpdateRulesetData, Ruleset } from '@/hooks/use-ruleset-queries';

// Form validation schema
const rulesetFormSchema = z.object({
  name: z.string().min(1, 'Ruleset name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  visibility: z.enum(['PUBLIC', 'PRIVATE']),
  ruleIds: z.array(z.string()).min(1, 'Please select at least one rule'),
});

type RulesetFormData = z.infer<typeof rulesetFormSchema>;

interface RulesetFormProps {
  initialData?: Ruleset;
  onSubmit: (data: CreateRulesetData | UpdateRulesetData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function RulesetForm({ initialData, onSubmit, onCancel, isLoading = false }: RulesetFormProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIDE, setSelectedIDE] = useState('ALL');
  const [showRuleSelection, setShowRuleSelection] = useState(false);

  // Form setup
  const form = useForm<RulesetFormData>({
    resolver: zodResolver(rulesetFormSchema),
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      visibility: initialData?.visibility || 'PRIVATE',
      ruleIds: initialData?.rules.map(r => r.rule.id) || [],
    },
  });

  // Fetch available rules
  const { data: rulesResponse, isLoading: rulesLoading } = useRules({
    search: searchQuery || undefined,
    ideType: selectedIDE !== 'ALL' ? selectedIDE : undefined,
  });

  const availableRules = rulesResponse?.rules || [];
  const selectedRuleIds = form.watch('ruleIds');

  // Get selected rules for display
  const selectedRules = availableRules.filter(rule => selectedRuleIds.includes(rule.id));

  const handleSubmit = async (data: RulesetFormData) => {
    try {
      if (initialData) {
        await onSubmit({ ...data, id: initialData.id } as UpdateRulesetData);
      } else {
        await onSubmit(data as CreateRulesetData);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const toggleRule = (ruleId: string) => {
    const currentIds = form.getValues('ruleIds');
    if (currentIds.includes(ruleId)) {
      form.setValue('ruleIds', currentIds.filter(id => id !== ruleId));
    } else {
      form.setValue('ruleIds', [...currentIds, ruleId]);
    }
  };

  const removeRule = (ruleId: string) => {
    const currentIds = form.getValues('ruleIds');
    form.setValue('ruleIds', currentIds.filter(id => id !== ruleId));
  };

  return (
    <Card className="max-w-4xl mx-auto">
      <Box p="6">
        <Heading size="6" mb="2">
          {initialData ? 'Edit Ruleset' : 'Create New Ruleset'}
        </Heading>
        <Text color="gray" mb="6">
          {initialData ? 'Update your ruleset details and rules' : 'Create a collection of AI rules for quick setup and deployment'}
        </Text>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <Box className="space-y-4">
              <Heading size="4">Basic Information</Heading>
              
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ruleset Name *</FormLabel>
                    <FormControl>
                      <TextField.Root
                        placeholder="Enter ruleset name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <TextArea
                        placeholder="Describe what this ruleset is for..."
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="visibility"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Visibility</FormLabel>
                    <FormControl>
                      <Select.Root value={field.value} onValueChange={field.onChange}>
                        <Select.Trigger>
                          <Flex align="center" gap="2">
                            {field.value === 'PUBLIC' ? (
                              <>
                                <Eye className="w-4 h-4" />
                                Public
                              </>
                            ) : (
                              <>
                                <Lock className="w-4 h-4" />
                                Private
                              </>
                            )}
                          </Flex>
                        </Select.Trigger>
                        <Select.Content>
                          <Select.Item value="PRIVATE">
                            <Flex align="center" gap="2">
                              <Lock className="w-4 h-4" />
                              Private
                            </Flex>
                          </Select.Item>
                          <Select.Item value="PUBLIC">
                            <Flex align="center" gap="2">
                              <Eye className="w-4 h-4" />
                              Public
                            </Flex>
                          </Select.Item>
                        </Select.Content>
                      </Select.Root>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </Box>

            <Separator size="4" />

            {/* Selected Rules */}
            <Box className="space-y-4">
              <Flex justify="between" align="center">
                <Heading size="4">Selected Rules ({selectedRules.length})</Heading>
                <Button
                  type="button"
                  variant="soft"
                  onClick={() => setShowRuleSelection(!showRuleSelection)}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  {showRuleSelection ? 'Hide' : 'Add'} Rules
                </Button>
              </Flex>

              {selectedRules.length > 0 ? (
                <Box className="space-y-2">
                  {selectedRules.map((rule) => (
                    <Card key={rule.id} variant="surface">
                      <Flex justify="between" align="center" p="3">
                        <Box className="flex-1">
                          <Text weight="medium">{rule.title}</Text>
                          {rule.description && (
                            <Text size="2" color="gray" className="line-clamp-1">
                              {rule.description}
                            </Text>
                          )}
                          <Flex gap="2" mt="1">
                            {rule.tags.slice(0, 2).map(({ tag }) => (
                              <Badge
                                key={tag.id}
                                variant="soft"
                                size="1"
                                style={{ backgroundColor: tag.color + '20', color: tag.color }}
                              >
                                {tag.name}
                              </Badge>
                            ))}
                          </Flex>
                        </Box>
                        <Button
                          type="button"
                          variant="ghost"
                          size="1"
                          onClick={() => removeRule(rule.id)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </Flex>
                    </Card>
                  ))}
                </Box>
              ) : (
                <Card variant="surface" className="text-center py-8">
                  <Text color="gray">No rules selected. Click "Add Rules" to get started.</Text>
                </Card>
              )}

              <FormField
                control={form.control}
                name="ruleIds"
                render={() => (
                  <FormItem>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </Box>

            {/* Rule Selection Panel */}
            {showRuleSelection && (
              <Card variant="surface">
                <Box p="4" className="space-y-4">
                  <Heading size="3">Add Rules to Ruleset</Heading>
                  
                  {/* Search and Filter */}
                  <Flex gap="3">
                    <Box className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <TextField.Root
                        placeholder="Search rules..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </Box>
                    <Select.Root value={selectedIDE} onValueChange={setSelectedIDE}>
                      <Select.Trigger className="w-48">
                        {selectedIDE === 'ALL' ? 'All IDEs' : selectedIDE}
                      </Select.Trigger>
                      <Select.Content>
                        <Select.Item value="ALL">All IDEs</Select.Item>
                        <Select.Item value="GENERAL">General</Select.Item>
                        <Select.Item value="CURSOR">Cursor</Select.Item>
                        <Select.Item value="AUGMENT">Augment Code</Select.Item>
                        <Select.Item value="WINDSURF">Windsurf</Select.Item>
                        <Select.Item value="CLAUDE">Claude</Select.Item>
                        <Select.Item value="GITHUB_COPILOT">GitHub Copilot</Select.Item>
                        <Select.Item value="GEMINI">Gemini</Select.Item>
                        <Select.Item value="OPENAI_CODEX">OpenAI Codex</Select.Item>
                        <Select.Item value="CLINE">Cline</Select.Item>
                      </Select.Content>
                    </Select.Root>
                  </Flex>

                  {/* Available Rules */}
                  <Box className="max-h-64 overflow-y-auto space-y-2">
                    {rulesLoading ? (
                      <Flex justify="center" py="4">
                        <Loader2 className="w-6 h-6 animate-spin" />
                      </Flex>
                    ) : availableRules.length === 0 ? (
                      <Text color="gray" className="text-center py-4">
                        No rules found. Try adjusting your search criteria.
                      </Text>
                    ) : (
                      availableRules.map((rule) => (
                        <Card key={rule.id} variant="surface">
                          <Flex align="center" gap="3" p="3">
                            <Checkbox
                              checked={selectedRuleIds.includes(rule.id)}
                              onCheckedChange={() => toggleRule(rule.id)}
                            />
                            <Box className="flex-1">
                              <Text weight="medium">{rule.title}</Text>
                              {rule.description && (
                                <Text size="2" color="gray" className="line-clamp-1">
                                  {rule.description}
                                </Text>
                              )}
                              <Flex gap="2" mt="1">
                                <Badge variant="soft" size="1">
                                  {rule.ideType}
                                </Badge>
                                {rule.tags.slice(0, 2).map(({ tag }) => (
                                  <Badge
                                    key={tag.id}
                                    variant="soft"
                                    size="1"
                                    style={{ backgroundColor: tag.color + '20', color: tag.color }}
                                  >
                                    {tag.name}
                                  </Badge>
                                ))}
                              </Flex>
                            </Box>
                          </Flex>
                        </Card>
                      ))
                    )}
                  </Box>
                </Box>
              </Card>
            )}

            {/* Form Actions */}
            <Flex justify="end" gap="3">
              <Button type="button" variant="soft" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                {initialData ? 'Update Ruleset' : 'Create Ruleset'}
              </Button>
            </Flex>
          </form>
        </Form>
      </Box>
    </Card>
  );
}
