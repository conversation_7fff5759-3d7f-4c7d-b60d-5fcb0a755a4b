'use client';

import Link from 'next/link';
import { <PERSON>, Badge, Button } from '@radix-ui/themes';
import { Clock, Eye, Lock, FileText } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface Rule {
  id: string;
  title: string;
  description: string | null;
  user: {
    id: string;
    name: string | null;
    email: string;
  };
  tags: Array<{
    tag: {
      id: string;
      name: string;
      color: string;
    };
  }>;
}

interface RulesetRule {
  order: number;
  rule: Rule;
}

interface Ruleset {
  id: string;
  name: string;
  description: string | null;
  visibility: 'PUBLIC' | 'PRIVATE';
  shareToken: string | null;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string | null;
    email: string;
  };
  rules: RulesetRule[];
}

interface RulesetCardProps {
  ruleset: Ruleset;
  onEdit?: (ruleset: Ruleset) => void;
  onDelete?: (ruleset: Ruleset) => void;
  showActions?: boolean;
}

export function RulesetCard({ ruleset, onEdit, onDelete, showActions = false }: RulesetCardProps) {
  const isPublic = ruleset.visibility === 'PUBLIC';
  const ruleCount = ruleset.rules.length;
  

  
  // Get all tags from rules
  const allTags = ruleset.rules.flatMap(r => r.rule.tags.map(t => t.tag));
  const uniqueTags = allTags.filter((tag, index, self) => 
    index === self.findIndex(t => t.id === tag.id)
  );

  return (
    <Card className="h-full flex flex-col p-4 space-y-4">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-semibold mb-2">{ruleset.name}</h3>
          {ruleset.description && (
            <p className="text-sm text-gray-600 line-clamp-2 mb-2">
              {ruleset.description}
            </p>
          )}
        </div>
        <div className="flex items-center gap-2 ml-4">
          {isPublic ? (
            <Badge color="green" variant="soft">
              <Eye className="w-3 h-3 mr-1" />
              Public
            </Badge>
          ) : (
            <Badge color="gray" variant="soft">
              <Lock className="w-3 h-3 mr-1" />
              Private
            </Badge>
          )}
        </div>
      </div>

      {/* Rule count and IDE types */}
      <div className="flex items-center justify-between text-sm text-gray-600">
        <div className="flex items-center gap-1">
          <FileText className="w-4 h-4" />
          <span>{ruleCount} rule{ruleCount !== 1 ? 's' : ''}</span>
        </div>
        <div className="flex items-center gap-1">
          {ideTypes.slice(0, 2).map(ideType => (
            <Badge key={ideType} variant="outline" className="text-xs">
              {ideType}
            </Badge>
          ))}
          {ideTypes.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{ideTypes.length - 2}
            </Badge>
          )}
        </div>
      </div>

      {/* Tags */}
      {uniqueTags.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {uniqueTags.slice(0, 5).map(tag => (
            <Badge 
              key={tag.id} 
              variant="soft" 
              className="text-xs"
              style={{ backgroundColor: tag.color + '20', color: tag.color }}
            >
              {tag.name}
            </Badge>
          ))}
          {uniqueTags.length > 5 && (
            <Badge variant="soft" className="text-xs">
              +{uniqueTags.length - 5}
            </Badge>
          )}
        </div>
      )}

      {/* Author and timestamp */}
      <div className="flex items-center justify-between text-sm text-gray-600">
        <span>
          {ruleset.user.name || ruleset.user.email}
        </span>
        <div className="flex items-center gap-1 text-xs">
          <Clock className="w-3 h-3" />
          <span>Updated {formatDistanceToNow(new Date(ruleset.updatedAt), { addSuffix: true })}</span>
        </div>
      </div>

      {/* Actions */}
      <div className="flex w-full gap-2 mt-auto">
        <Button asChild variant="solid" className="flex-1">
          <Link href={`/rulesets/${ruleset.id}${isPublic && ruleset.shareToken ? `?token=${ruleset.shareToken}` : ''}`}>
            View Ruleset
          </Link>
        </Button>
        
        {showActions && (
          <>
            {onEdit && (
              <Button variant="outline" onClick={() => onEdit(ruleset)}>
                Edit
              </Button>
            )}
            {onDelete && (
              <Button variant="outline" color="red" onClick={() => onDelete(ruleset)}>
                Delete
              </Button>
            )}
          </>
        )}
      </div>
    </Card>
  );
}