'use client';

import { useState, useEffect } from 'react';
import { Card, Badge, Button, TextField, TextArea, Select } from '@radix-ui/themes';
import { Loader2, Search, Eye, Lock } from 'lucide-react';

interface Rule {
  id: string;
  title: string;
  description: string | null;
  visibility: 'PUBLIC' | 'PRIVATE';
  user: {
    id: string;
    name: string | null;
    email: string;
  };
  tags: Array<{
    tag: {
      id: string;
      name: string;
      color: string;
    };
  }>;
}

interface RulesetRule {
  order: number;
  rule: Rule;
}

interface Ruleset {
  id?: string;
  name: string;
  description: string | null;
  visibility: 'PUBLIC' | 'PRIVATE';
  rules: RulesetRule[];
}

interface RulesetEditorProps {
  initialRuleset?: Ruleset;
  onSave: (ruleset: Omit<Ruleset, 'id' | 'rules'> & { ruleIds: string[] }) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function RulesetEditor({ initialRuleset, onSave, onCancel, isLoading = false }: RulesetEditorProps) {
  const [name, setName] = useState(initialRuleset?.name || '');
  const [description, setDescription] = useState(initialRuleset?.description || '');
  const [visibility, setVisibility] = useState<'PUBLIC' | 'PRIVATE'>(initialRuleset?.visibility || 'PRIVATE');
  const [selectedRuleIds, setSelectedRuleIds] = useState<string[]>(
    initialRuleset?.rules.map(r => r.rule.id) || []
  );
  
  const [rules, setRules] = useState<Rule[]>([]);
  const [rulesLoading, setRulesLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Fetch available rules
  useEffect(() => {
    const fetchRules = async () => {
      try {
        setRulesLoading(true);
        const response = await fetch('/api/rules');
        if (!response.ok) {
          throw new Error('Failed to fetch rules');
        }
        const rulesData = await response.json();
        setRules(rulesData);
      } catch (error) {
        console.error('Error fetching rules:', error);
        setError('Failed to load rules');
      } finally {
        setRulesLoading(false);
      }
    };

    fetchRules();
  }, []);

  const filteredRules = rules.filter(rule =>
    rule.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.tags.some(t => t.tag.name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleRuleToggle = (ruleId: string) => {
    setSelectedRuleIds(prev =>
      prev.includes(ruleId)
        ? prev.filter(id => id !== ruleId)
        : [...prev, ruleId]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      setError('Ruleset name is required');
      return;
    }

    if (selectedRuleIds.length === 0) {
      setError('Please select at least one rule');
      return;
    }

    try {
      setError(null);
      await onSave({
        name: name.trim(),
        description: description.trim() || null,
        visibility,
        ruleIds: selectedRuleIds,
      });
    } catch (error) {
      console.error('Error saving ruleset:', error);
      setError('Failed to save ruleset');
    }
  };

  return (
    <div className="space-y-6 max-w-4xl mx-auto p-6">
      <Card>
        <div className="p-6">
          <h2 className="text-2xl font-bold mb-2">
            {initialRuleset ? 'Edit Ruleset' : 'Create New Ruleset'}
          </h2>
          <p className="text-gray-600 mb-6">
            Create a collection of AI rules for quick setup and deployment
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            )}

            {/* Basic Info */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Ruleset Name</label>
                <TextField.Root
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter ruleset name..."
                  disabled={isLoading}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Description (Optional)</label>
                <TextArea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe what this ruleset is for..."
                  rows={3}
                  disabled={isLoading}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Visibility</label>
                <Select.Root
                  value={visibility}
                  onValueChange={(value: 'PUBLIC' | 'PRIVATE') => setVisibility(value)}
                  disabled={isLoading}
                >
                  <Select.Trigger className="w-full" />
                  <Select.Content>
                    <Select.Item value="PRIVATE">
                      <div className="flex items-center gap-2">
                        <Lock className="w-4 h-4" />
                        Private - Only you can access
                      </div>
                    </Select.Item>
                    <Select.Item value="PUBLIC">
                      <div className="flex items-center gap-2">
                        <Eye className="w-4 h-4" />
                        Public - Anyone can view
                      </div>
                    </Select.Item>
                  </Select.Content>
                </Select.Root>
              </div>
            </div>

            <hr className="border-gray-200" />

            {/* Rule Selection */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Select Rules ({selectedRuleIds.length} selected)
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <TextField.Root
                    placeholder="Search rules..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-full"
                    disabled={isLoading || rulesLoading}
                  />
                </div>
              </div>

              <div className="border rounded-md p-4 h-96 overflow-y-auto">
                {rulesLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="w-6 h-6 animate-spin" />
                    <span className="ml-2">Loading rules...</span>
                  </div>
                ) : filteredRules.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    {searchTerm ? 'No rules found matching your search' : 'No rules available'}
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredRules.map((rule) => (
                      <div
                        key={rule.id}
                        className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <input
                          type="checkbox"
                          id={rule.id}
                          checked={selectedRuleIds.includes(rule.id)}
                          onChange={() => handleRuleToggle(rule.id)}
                          disabled={isLoading}
                          className="mt-1"
                        />
                        <div className="flex-1 min-w-0">
                          <label
                            htmlFor={rule.id}
                            className="block font-medium cursor-pointer mb-1"
                          >
                            {rule.title}
                          </label>
                          {rule.description && (
                            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                              {rule.description}
                            </p>
                          )}
                          <div className="flex items-center gap-2 flex-wrap">
                            {rule.visibility === 'PUBLIC' ? (
                              <Badge color="green" variant="soft" className="text-xs">
                                <Eye className="w-3 h-3 mr-1" />
                                Public
                              </Badge>
                            ) : (
                              <Badge color="gray" variant="soft" className="text-xs">
                                <Lock className="w-3 h-3 mr-1" />
                                Private
                              </Badge>
                            )}
                            {rule.tags.slice(0, 3).map(({ tag }) => (
                              <Badge
                                key={tag.id}
                                variant="soft"
                                className="text-xs"
                                style={{ backgroundColor: tag.color + '20', color: tag.color }}
                              >
                                {tag.name}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3">
              <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isLoading || !name.trim() || selectedRuleIds.length === 0}
                variant="solid"
              >
                {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                {initialRuleset ? 'Update Ruleset' : 'Create Ruleset'}
              </Button>
            </div>
          </form>
        </div>
      </Card>
    </div>
  );
}